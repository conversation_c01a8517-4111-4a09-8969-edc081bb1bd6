from openai import OpenAI
from dotenv import load_dotenv
import os
load_dotenv()
api_key=os.getenv("OPENAI_API_KEY")   
api_url=os.getenv("OPENAI_API_BASE")
client = OpenAI(api_key=api_key, base_url=api_url)
messages = [
    {"role": "system", "content": "Translate the following from English into Italian"},
    {"role": "user", "content": "hi!"}
]
result = client.chat.completions.create(
    model="gpt-3.5-turbo",
    messages=messages,
    temperature=0.2,
    max_tokens=100
)
print(result.choices[0].message.content)
